"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_error";
exports.ids = ["pages/_error"];
exports.modules = {

/***/ "./pages/_error.js":
/*!*************************!*\
  !*** ./pages/_error.js ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nvar _jsxFileName = \"/Users/<USER>/projects/gankao/aiword.gankao.com/pages/_error.js\";\n\nvar __jsx = (react__WEBPACK_IMPORTED_MODULE_0___default().createElement);\n\nfunction Error(_ref) {\n  var statusCode = _ref.statusCode;\n  return __jsx(\"div\", {\n    style: {\n      backgroundColor: '#1890ff',\n      color: 'white',\n      minHeight: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      fontFamily: '-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif',\n      padding: '20px',\n      textAlign: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 3,\n      columnNumber: 5\n    }\n  }, __jsx(\"div\", {\n    style: {\n      maxWidth: '600px',\n      padding: '40px',\n      borderRadius: '8px',\n      backgroundColor: 'rgba(0, 60, 136, 0.5)',\n      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }\n  }, __jsx(\"h1\", {\n    style: {\n      fontSize: '32px',\n      marginBottom: '20px',\n      fontWeight: '500'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 9\n    }\n  }, statusCode ? \"\".concat(statusCode, \" \\u9519\\u8BEF\") : '出错了'), __jsx(\"p\", {\n    style: {\n      fontSize: '18px',\n      lineHeight: '1.6',\n      marginBottom: '30px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 9\n    }\n  }, statusCode ? \"\\u62B1\\u6B49\\uFF0C\\u670D\\u52A1\\u5668\\u8FD4\\u56DE\\u4E86 \".concat(statusCode, \" \\u9519\\u8BEF\") : '抱歉，客户端发生了错误'), __jsx(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 9\n    }\n  }, __jsx(\"button\", {\n    onClick: () => window.location.href = '/p-aiword/landing',\n    style: {\n      backgroundColor: 'white',\n      color: '#1890ff',\n      border: 'none',\n      padding: '10px 20px',\n      borderRadius: '4px',\n      fontSize: '16px',\n      cursor: 'pointer',\n      fontWeight: '500',\n      transition: 'all 0.3s ease'\n    },\n    onMouseOver: e => e.currentTarget.style.backgroundColor = '#f0f0f0',\n    onMouseOut: e => e.currentTarget.style.backgroundColor = 'white',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 11\n    }\n  }, \"\\u8FD4\\u56DE\\u9996\\u9875\"))));\n}\n\nError.getInitialProps = _ref2 => {\n  var res = _ref2.res,\n      err = _ref2.err;\n  var statusCode = res ? res.statusCode : err ? err.statusCode : 404;\n  return {\n    statusCode\n  };\n};\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Error);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_error.js\n");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/_error.js"));
module.exports = __webpack_exports__;

})();