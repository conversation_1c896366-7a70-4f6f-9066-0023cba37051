/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_next_dist_pages__error_js"],{

/***/ "./node_modules/next/dist/pages/_error.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/pages/_error.js ***!
  \************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval("\n\nvar _Object$defineProperty = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/object/define-property */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/object/define-property.js\");\n\nvar _concatInstanceProperty = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/instance/concat */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/instance/concat.js\");\n\n_Object$defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nexports[\"default\"] = void 0;\n\nvar _react = _interopRequireDefault(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\n\nvar _head = _interopRequireDefault(__webpack_require__(/*! ../shared/lib/head */ \"./node_modules/next/dist/shared/lib/head.js\"));\n\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\n\nvar statusCodes = {\n  400: 'Bad Request',\n  404: 'This page could not be found',\n  405: 'Method Not Allowed',\n  500: 'Internal Server Error'\n};\n\nfunction _getInitialProps({\n  res,\n  err\n}) {\n  var statusCode = res && res.statusCode ? res.statusCode : err ? err.statusCode : 404;\n  return {\n    statusCode\n  };\n}\n\nclass Error extends _react.default.Component {\n  render() {\n    var _context;\n\n    var statusCode = this.props.statusCode;\n    var title = this.props.title || statusCodes[statusCode] || 'An unexpected error has occurred';\n    return /*#__PURE__*/_react.default.createElement(\"div\", {\n      style: styles.error\n    }, /*#__PURE__*/_react.default.createElement(_head.default, null, /*#__PURE__*/_react.default.createElement(\"title\", null, statusCode ? _concatInstanceProperty(_context = \"\".concat(statusCode, \": \")).call(_context, title) : 'Application error: a client-side exception has occurred')), /*#__PURE__*/_react.default.createElement(\"div\", null, /*#__PURE__*/_react.default.createElement(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"\\n                body { margin: 0; color: #000; background: #fff; }\\n                .next-error-h1 {\\n                  border-right: 1px solid rgba(0, 0, 0, .3);\\n                }\\n                @media (prefers-color-scheme: dark) {\\n                  body { color: #fff; background: #000; }\\n                  .next-error-h1 {\\n                    border-right: 1px solid rgba(255, 255, 255, .3);\\n                  }\\n                }\"\n      }\n    }), statusCode ? /*#__PURE__*/_react.default.createElement(\"h1\", {\n      className: \"next-error-h1\",\n      style: styles.h1\n    }, statusCode) : null, /*#__PURE__*/_react.default.createElement(\"div\", {\n      style: styles.desc\n    }, /*#__PURE__*/_react.default.createElement(\"h2\", {\n      style: styles.h2\n    }, this.props.title || statusCode ? title : /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, \"Application error: a client-side exception has occurred (see the browser console for more information)\"), \".\"))));\n  }\n\n}\n\nexports[\"default\"] = Error;\nError.displayName = 'ErrorPage';\nError.getInitialProps = _getInitialProps;\nError.origGetInitialProps = _getInitialProps;\nvar styles = {\n  error: {\n    fontFamily: '-apple-system, BlinkMacSystemFont, Roboto, \"Segoe UI\", \"Fira Sans\", Avenir, \"Helvetica Neue\", \"Lucida Grande\", sans-serif',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  desc: {\n    display: 'inline-block',\n    textAlign: 'left',\n    lineHeight: '49px',\n    height: '49px',\n    verticalAlign: 'middle'\n  },\n  h1: {\n    display: 'inline-block',\n    margin: 0,\n    marginRight: '20px',\n    padding: '10px 23px 10px 0',\n    fontSize: '24px',\n    fontWeight: 500,\n    verticalAlign: 'top'\n  },\n  h2: {\n    fontSize: '14px',\n    fontWeight: 'normal',\n    lineHeight: 'inherit',\n    margin: 0,\n    padding: 0\n  }\n};\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/pages/_error.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/amp-context.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/amp-context.js ***!
  \**********************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval("\n\nvar _Object$defineProperty = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/object/define-property */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/object/define-property.js\");\n\n_Object$defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nexports.AmpStateContext = void 0;\n\nvar _react = _interopRequireDefault(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\n\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\n\nvar AmpStateContext = _react.default.createContext({});\n\nexports.AmpStateContext = AmpStateContext;\n\nif (true) {\n  AmpStateContext.displayName = 'AmpStateContext';\n}\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvYW1wLWNvbnRleHQuanMuanMiLCJtYXBwaW5ncyI6IkFBQWE7Ozs7QUFDYix1QkFBc0JBLE9BQXRCLEVBQStCLFlBQS9CLEVBQTZDO0FBQ3pDQyxFQUFBQSxLQUFLLEVBQUU7QUFEa0MsQ0FBN0M7O0FBR0FELHVCQUFBLEdBQTBCLEtBQUssQ0FBL0I7O0FBQ0EsSUFBSUcsTUFBTSxHQUFHQyxzQkFBc0IsQ0FBQ0MsbUJBQU8sQ0FBQyw0Q0FBRCxDQUFSLENBQW5DOztBQUNBLFNBQVNELHNCQUFULENBQWdDRSxHQUFoQyxFQUFxQztBQUNqQyxTQUFPQSxHQUFHLElBQUlBLEdBQUcsQ0FBQ0MsVUFBWCxHQUF3QkQsR0FBeEIsR0FBOEI7QUFDakNFLElBQUFBLE9BQU8sRUFBRUY7QUFEd0IsR0FBckM7QUFHSDs7QUFDRCxJQUFNSixlQUFlLEdBQUdDLE1BQU0sQ0FBQ0ssT0FBUCxDQUFlQyxhQUFmLENBQTZCLEVBQTdCLENBQXhCOztBQUNBVCx1QkFBQSxHQUEwQkUsZUFBMUI7O0FBQ0EsSUFBSSxNQUF1QztBQUN2Q0EsRUFBQUEsZUFBZSxDQUFDUSxXQUFoQixHQUE4QixpQkFBOUI7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvYW1wLWNvbnRleHQuanM/ZTVhNyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHMuQW1wU3RhdGVDb250ZXh0ID0gdm9pZCAwO1xudmFyIF9yZWFjdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcInJlYWN0XCIpKTtcbmZ1bmN0aW9uIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQob2JqKSB7XG4gICAgcmV0dXJuIG9iaiAmJiBvYmouX19lc01vZHVsZSA/IG9iaiA6IHtcbiAgICAgICAgZGVmYXVsdDogb2JqXG4gICAgfTtcbn1cbmNvbnN0IEFtcFN0YXRlQ29udGV4dCA9IF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUNvbnRleHQoe30pO1xuZXhwb3J0cy5BbXBTdGF0ZUNvbnRleHQgPSBBbXBTdGF0ZUNvbnRleHQ7XG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICAgIEFtcFN0YXRlQ29udGV4dC5kaXNwbGF5TmFtZSA9ICdBbXBTdGF0ZUNvbnRleHQnO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hbXAtY29udGV4dC5qcy5tYXAiXSwibmFtZXMiOlsiZXhwb3J0cyIsInZhbHVlIiwiQW1wU3RhdGVDb250ZXh0IiwiX3JlYWN0IiwiX2ludGVyb3BSZXF1aXJlRGVmYXVsdCIsInJlcXVpcmUiLCJvYmoiLCJfX2VzTW9kdWxlIiwiZGVmYXVsdCIsImNyZWF0ZUNvbnRleHQiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/amp-context.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/amp.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/amp.js ***!
  \**************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval("\n\nvar _Object$defineProperty = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/object/define-property */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/object/define-property.js\");\n\nvar _Object$assign = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/object/assign */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/object/assign.js\");\n\nvar _s = $RefreshSig$();\n\n_Object$defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nexports.isInAmpMode = isInAmpMode;\nexports.useAmp = useAmp;\n\nvar _react = _interopRequireDefault(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\n\nvar _ampContext = __webpack_require__(/*! ./amp-context */ \"./node_modules/next/dist/shared/lib/amp-context.js\");\n\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\n\nfunction isInAmpMode({\n  ampFirst = false,\n  hybrid = false,\n  hasQuery = false\n} = {}) {\n  return ampFirst || hybrid && hasQuery;\n}\n\nfunction useAmp() {\n  _s();\n\n  // Don't assign the context value to a variable to save bytes\n  return isInAmpMode(_react.default.useContext(_ampContext.AmpStateContext));\n}\n\n_s(useAmp, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n\nif (typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) {\n  _Object$assign(exports.default, exports);\n\n  module.exports = exports.default;\n}\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/amp.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/head.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/head.js ***!
  \***************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval("\n\n__webpack_require__(/*! core-js/modules/web.dom-collections.for-each.js */ \"./node_modules/core-js/modules/web.dom-collections.for-each.js\");\n\nvar _Object$keys = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/object/keys */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/object/keys.js\");\n\nvar _Object$getOwnPropertySymbols = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/object/get-own-property-symbols */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/object/get-own-property-symbols.js\");\n\nvar _Object$getOwnPropertyDescriptors = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/object/get-own-property-descriptors */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/object/get-own-property-descriptors.js\");\n\nvar _Object$defineProperties = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/object/define-properties */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/object/define-properties.js\");\n\nvar _defineProperty = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/defineProperty */ \"./node_modules/next/dist/compiled/@babel/runtime/helpers/defineProperty.js\");\n\nvar _Object$defineProperty = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/object/define-property */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/object/define-property.js\");\n\nvar _Object$getOwnPropertyDescriptor = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/object/get-own-property-descriptor */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/object/get-own-property-descriptor.js\");\n\nvar _concatInstanceProperty = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/instance/concat */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/instance/concat.js\");\n\nvar _reduceInstanceProperty = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/instance/reduce */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/instance/reduce.js\");\n\nvar _Set = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/set */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/set.js\");\n\nvar _indexOfInstanceProperty = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/instance/index-of */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/instance/index-of.js\");\n\nvar _sliceInstanceProperty = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/instance/slice */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/instance/slice.js\");\n\nvar _mapInstanceProperty = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/instance/map */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/instance/map.js\");\n\nvar _reverseInstanceProperty = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/instance/reverse */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/instance/reverse.js\");\n\nvar _filterInstanceProperty = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/instance/filter */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/instance/filter.js\");\n\nvar _startsWithInstanceProperty = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/instance/starts-with */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/instance/starts-with.js\");\n\nvar _Object$assign = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/object/assign */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/object/assign.js\");\n\nvar _s = $RefreshSig$();\n\nfunction ownKeys(object, enumerableOnly) { var keys = _Object$keys(object); if (_Object$getOwnPropertySymbols) { var symbols = _Object$getOwnPropertySymbols(object); if (enumerableOnly) { symbols = _filterInstanceProperty(symbols).call(symbols, function (sym) { return _Object$getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (_Object$getOwnPropertyDescriptors) { _Object$defineProperties(target, _Object$getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { _Object$defineProperty(target, key, _Object$getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\n_Object$defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nexports.defaultHead = defaultHead;\nexports[\"default\"] = void 0;\n\nvar _react = _interopRequireWildcard(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\n\nvar _sideEffect = _interopRequireDefault(__webpack_require__(/*! ./side-effect */ \"./node_modules/next/dist/shared/lib/side-effect.js\"));\n\nvar _ampContext = __webpack_require__(/*! ./amp-context */ \"./node_modules/next/dist/shared/lib/amp-context.js\");\n\nvar _headManagerContext = __webpack_require__(/*! ./head-manager-context */ \"./node_modules/next/dist/shared/lib/head-manager-context.js\");\n\nvar _amp = __webpack_require__(/*! ./amp */ \"./node_modules/next/dist/shared/lib/amp.js\");\n\nvar _utils = __webpack_require__(/*! ./utils */ \"./node_modules/next/dist/shared/lib/utils.js\");\n\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\n\nfunction _interopRequireWildcard(obj) {\n  if (obj && obj.__esModule) {\n    return obj;\n  } else {\n    var newObj = {};\n\n    if (obj != null) {\n      for (var key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key)) {\n          var desc = _Object$defineProperty && _Object$getOwnPropertyDescriptor ? _Object$getOwnPropertyDescriptor(obj, key) : {};\n\n          if (desc.get || desc.set) {\n            _Object$defineProperty(newObj, key, desc);\n          } else {\n            newObj[key] = obj[key];\n          }\n        }\n      }\n    }\n\n    newObj.default = obj;\n    return newObj;\n  }\n}\n\nfunction defaultHead(inAmpMode = false) {\n  var head = [/*#__PURE__*/_react.default.createElement(\"meta\", {\n    charSet: \"utf-8\"\n  })];\n\n  if (!inAmpMode) {\n    head.push( /*#__PURE__*/_react.default.createElement(\"meta\", {\n      name: \"viewport\",\n      content: \"width=device-width\"\n    }));\n  }\n\n  return head;\n}\n\nfunction onlyReactElement(list, child) {\n  // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n  if (typeof child === 'string' || typeof child === 'number') {\n    return list;\n  } // Adds support for React.Fragment\n\n\n  if (child.type === _react.default.Fragment) {\n    var _context;\n\n    return _concatInstanceProperty(list).call(list, _reduceInstanceProperty(_context = _react.default.Children.toArray(child.props.children)).call(_context, (fragmentList, fragmentChild) => {\n      if (typeof fragmentChild === 'string' || typeof fragmentChild === 'number') {\n        return fragmentList;\n      }\n\n      return _concatInstanceProperty(fragmentList).call(fragmentList, fragmentChild);\n    }, []));\n  }\n\n  return _concatInstanceProperty(list).call(list, child);\n}\n\nvar METATYPES = ['name', 'httpEquiv', 'charSet', 'itemProp'];\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/\n\nfunction unique() {\n  var keys = new _Set();\n  var tags = new _Set();\n  var metaTypes = new _Set();\n  var metaCategories = {};\n  return h => {\n    var _context2;\n\n    var isUnique = true;\n    var hasKey = false;\n\n    if (h.key && typeof h.key !== 'number' && _indexOfInstanceProperty(_context2 = h.key).call(_context2, '$') > 0) {\n      var _context3, _context4;\n\n      hasKey = true;\n\n      var key = _sliceInstanceProperty(_context3 = h.key).call(_context3, _indexOfInstanceProperty(_context4 = h.key).call(_context4, '$') + 1);\n\n      if (keys.has(key)) {\n        isUnique = false;\n      } else {\n        keys.add(key);\n      }\n    } // eslint-disable-next-line default-case\n\n\n    switch (h.type) {\n      case 'title':\n      case 'base':\n        if (tags.has(h.type)) {\n          isUnique = false;\n        } else {\n          tags.add(h.type);\n        }\n\n        break;\n\n      case 'meta':\n        for (var i = 0, len = METATYPES.length; i < len; i++) {\n          var metatype = METATYPES[i];\n          if (!h.props.hasOwnProperty(metatype)) continue;\n\n          if (metatype === 'charSet') {\n            if (metaTypes.has(metatype)) {\n              isUnique = false;\n            } else {\n              metaTypes.add(metatype);\n            }\n          } else {\n            var category = h.props[metatype];\n            var categories = metaCategories[metatype] || new _Set();\n\n            if ((metatype !== 'name' || !hasKey) && categories.has(category)) {\n              isUnique = false;\n            } else {\n              categories.add(category);\n              metaCategories[metatype] = categories;\n            }\n          }\n        }\n\n        break;\n    }\n\n    return isUnique;\n  };\n}\n/**\n *\n * @param headElements List of multiple <Head> instances\n */\n\n\nfunction reduceComponents(headElements, props) {\n  var _context5, _context6, _context7, _context8, _context9, _context10;\n\n  return _mapInstanceProperty(_context5 = _reverseInstanceProperty(_context6 = _filterInstanceProperty(_context7 = _concatInstanceProperty(_context8 = _reverseInstanceProperty(_context9 = _reduceInstanceProperty(_context10 = _reduceInstanceProperty(headElements).call(headElements, (list, headElement) => {\n    var headElementChildren = _react.default.Children.toArray(headElement.props.children);\n\n    return _concatInstanceProperty(list).call(list, headElementChildren);\n  }, [])).call(_context10, onlyReactElement, [])).call(_context9)).call(_context8, defaultHead(props.inAmpMode))).call(_context7, unique())).call(_context6)).call(_context5, (c, i) => {\n    var key = c.key || i;\n\n    if (false) { var newProps; }\n\n    if (false) { var srcMessage; }\n\n    return /*#__PURE__*/_react.default.cloneElement(c, {\n      key\n    });\n  });\n}\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */\n\n\nfunction Head({\n  children\n}) {\n  _s();\n\n  var ampState = (0, _react).useContext(_ampContext.AmpStateContext);\n  var headManager = (0, _react).useContext(_headManagerContext.HeadManagerContext);\n  return /*#__PURE__*/_react.default.createElement(_sideEffect.default, {\n    reduceComponentsToState: reduceComponents,\n    headManager: headManager,\n    inAmpMode: (0, _amp).isInAmpMode(ampState)\n  }, children);\n}\n\n_s(Head, \"sCUayZmr5V93tUjujy03KdMBCec=\");\n\n_c = Head;\nvar _default = Head;\nexports[\"default\"] = _default;\n\nif (typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) {\n  _Object$assign(exports.default, exports);\n\n  module.exports = exports.default;\n}\n\nvar _c;\n\n$RefreshReg$(_c, \"Head\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/head.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/side-effect.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/side-effect.js ***!
  \**********************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval("\n\nvar _Object$defineProperty = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/object/define-property */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/object/define-property.js\");\n\nvar _Object$getOwnPropertyDescriptor = __webpack_require__(/*! @babel/runtime-corejs3/core-js-stable/object/get-own-property-descriptor */ \"./node_modules/@babel/runtime-corejs3/core-js-stable/object/get-own-property-descriptor.js\");\n\n__webpack_require__(/*! core-js/modules/es.array.iterator.js */ \"./node_modules/core-js/modules/es.array.iterator.js\");\n\n__webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ \"./node_modules/core-js/modules/web.dom-collections.iterator.js\");\n\n_Object$defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nexports[\"default\"] = void 0;\n\nvar _react = _interopRequireWildcard(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\n\nfunction _interopRequireWildcard(obj) {\n  if (obj && obj.__esModule) {\n    return obj;\n  } else {\n    var newObj = {};\n\n    if (obj != null) {\n      for (var key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key)) {\n          var desc = _Object$defineProperty && _Object$getOwnPropertyDescriptor ? _Object$getOwnPropertyDescriptor(obj, key) : {};\n\n          if (desc.get || desc.set) {\n            _Object$defineProperty(newObj, key, desc);\n          } else {\n            newObj[key] = obj[key];\n          }\n        }\n      }\n    }\n\n    newObj.default = obj;\n    return newObj;\n  }\n}\n\nvar isServer = false;\n\nclass _class extends _react.Component {\n  constructor(props) {\n    super(props);\n\n    this.emitChange = () => {\n      if (this._hasHeadManager) {\n        this.props.headManager.updateHead(this.props.reduceComponentsToState([...this.props.headManager.mountedInstances], this.props));\n      }\n    };\n\n    this._hasHeadManager = this.props.headManager && this.props.headManager.mountedInstances;\n\n    if (isServer && this._hasHeadManager) {\n      this.props.headManager.mountedInstances.add(this);\n      this.emitChange();\n    }\n  }\n\n  componentDidMount() {\n    if (this._hasHeadManager) {\n      this.props.headManager.mountedInstances.add(this);\n    }\n\n    this.emitChange();\n  }\n\n  componentDidUpdate() {\n    this.emitChange();\n  }\n\n  componentWillUnmount() {\n    if (this._hasHeadManager) {\n      this.props.headManager.mountedInstances.delete(this);\n    }\n\n    this.emitChange();\n  }\n\n  render() {\n    return null;\n  }\n\n}\n\nexports[\"default\"] = _class;\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/side-effect.js\n");

/***/ }),

/***/ "./node_modules/@babel/runtime-corejs3/core-js-stable/instance/reverse.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/instance/reverse.js ***!
  \********************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("module.exports = __webpack_require__(/*! core-js-pure/stable/instance/reverse */ \"./node_modules/core-js-pure/stable/instance/reverse.js\");//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9jb3JlLWpzLXN0YWJsZS9pbnN0YW5jZS9yZXZlcnNlLmpzLmpzIiwibWFwcGluZ3MiOiJBQUFBLDBJQUFnRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9jb3JlLWpzLXN0YWJsZS9pbnN0YW5jZS9yZXZlcnNlLmpzPzNiZDQiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKFwiY29yZS1qcy1wdXJlL3N0YWJsZS9pbnN0YW5jZS9yZXZlcnNlXCIpOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/@babel/runtime-corejs3/core-js-stable/instance/reverse.js\n");

/***/ })

}]);