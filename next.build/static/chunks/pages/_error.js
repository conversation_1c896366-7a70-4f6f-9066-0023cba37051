/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/_error"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_error&page=%2F_error!":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_error&page=%2F_error! ***!
  \***********************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/_error\",\n      function () {\n        return __webpack_require__(/*! private-next-pages/_error */ \"./pages/_error.js\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/_error\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1wcml2YXRlLW5leHQtcGFnZXMlMkZfZXJyb3ImcGFnZT0lMkZfZXJyb3IhLmpzIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsb0RBQTJCO0FBQ2xEO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz84MDdiIl0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvX2Vycm9yXCIsXG4gICAgICBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiByZXF1aXJlKFwicHJpdmF0ZS1uZXh0LXBhZ2VzL19lcnJvclwiKTtcbiAgICAgIH1cbiAgICBdKTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoZnVuY3Rpb24gKCkge1xuICAgICAgICB3aW5kb3cuX19ORVhUX1AucHVzaChbXCIvX2Vycm9yXCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_error&page=%2F_error!\n");

/***/ }),

/***/ "./pages/_error.js":
/*!*************************!*\
  !*** ./pages/_error.js ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nvar _jsxFileName = \"/Users/<USER>/projects/gankao/aiword.gankao.com/pages/_error.js\";\n\nvar __jsx = (react__WEBPACK_IMPORTED_MODULE_0___default().createElement);\n\nfunction Error(_ref) {\n  var statusCode = _ref.statusCode;\n  return __jsx(\"div\", {\n    style: {\n      backgroundColor: '#1890ff',\n      color: 'white',\n      minHeight: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      fontFamily: '-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif',\n      padding: '20px',\n      textAlign: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 3,\n      columnNumber: 5\n    }\n  }, __jsx(\"div\", {\n    style: {\n      maxWidth: '600px',\n      padding: '40px',\n      borderRadius: '8px',\n      backgroundColor: 'rgba(0, 60, 136, 0.5)',\n      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }\n  }, __jsx(\"h1\", {\n    style: {\n      fontSize: '32px',\n      marginBottom: '20px',\n      fontWeight: '500'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 9\n    }\n  }, statusCode ? \"\".concat(statusCode, \" \\u9519\\u8BEF\") : '出错了'), __jsx(\"p\", {\n    style: {\n      fontSize: '18px',\n      lineHeight: '1.6',\n      marginBottom: '30px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 9\n    }\n  }, statusCode ? \"\\u62B1\\u6B49\\uFF0C\\u670D\\u52A1\\u5668\\u8FD4\\u56DE\\u4E86 \".concat(statusCode, \" \\u9519\\u8BEF\") : '抱歉，客户端发生了错误'), __jsx(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 9\n    }\n  }, __jsx(\"button\", {\n    onClick: () => window.location.href = '/p-aiword/landing',\n    style: {\n      backgroundColor: 'white',\n      color: '#1890ff',\n      border: 'none',\n      padding: '10px 20px',\n      borderRadius: '4px',\n      fontSize: '16px',\n      cursor: 'pointer',\n      fontWeight: '500',\n      transition: 'all 0.3s ease'\n    },\n    onMouseOver: e => e.currentTarget.style.backgroundColor = '#f0f0f0',\n    onMouseOut: e => e.currentTarget.style.backgroundColor = 'white',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 11\n    }\n  }, \"\\u8FD4\\u56DE\\u9996\\u9875\"))));\n}\n\n_c = Error;\n\nError.getInitialProps = _ref2 => {\n  var res = _ref2.res,\n      err = _ref2.err;\n  var statusCode = res ? res.statusCode : err ? err.statusCode : 404;\n  return {\n    statusCode\n  };\n};\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (Error);\n\nvar _c;\n\n$RefreshReg$(_c, \"Error\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_error.js\n");

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_error&page=%2F_error!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);