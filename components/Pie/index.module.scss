.pie_container {
	overflow: auto;
}
.pie_container .pie_in {
	position: relative;
}
// 新的包装器样式，用于整体布局
.pie_container .pie_in .pie_wrapper {
	display: flex;
	flex-wrap: wrap;
	align-items: flex-start;
	justify-content: flex-start;
}

// 每个pie项目（包含标题和内容）
.pie_container .pie_in .pie_wrapper .pie_item {
	display: flex;
	flex-direction: column;
	flex: 1;
	min-width: 0;
}

// 保留原有的标题背景样式，但应用到整个wrapper
.pie_container .pie_in .pie_wrapper::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 50px; // 标题区域的高度
	background: linear-gradient(to right, #2974a3, #15cd8f);
	z-index: 1;
	-webkit-clip-path: polygon(
		16px 0,
		100% 0%,
		100% calc(100% - 16px),
		calc(100% - 16px) 100%,
		0 100%,
		0 16px
	);
}

.pie_container .pie_in .pie_left_header_tip {
	position: absolute;
	top: -10px;
	left: -10px;
	width: 20px;
	height: 20px;
	background: #5338c4;
	z-index: 1000;
	transform: rotate(45deg);
	border-right: 1px solid #43f0ff;
}
.pie_container .pie_in .pie_left_header_tip img {
	position: absolute;
	right: -10px;
	transform: rotate(-225deg) scale(0.6);
}

.pie_container .pie_in .pie_right_header_tip {
	position: absolute;
	right: -10px;
	top: 33px;
	width: 20px;
	height: 20px;
	background: #5338c4;
	z-index: 1000;
	transform: rotate(45deg);
	border-left: 1px solid #43f0ff;
}

.pie_container .pie_in .pie_right_header_tip img {
	position: absolute;
	left: -10px;
	top: -2px;
	transform: rotate(-225deg) scale(0.6);
}
.pie_container .pie_in .pie_left_header_tip-line {
	width: 100%;
	height: 1px;
	background: #e79f4c;
	margin: 2px auto;
	transform: rotate(-265deg);
	transform-origin: 50% 50%;
}
.pie_container .pie_in .pie_left_header_tip-line:last-child {
	width: 80%;
	height: 1px;
	background: #e79f4c;
	margin: 2px auto;
}

// 标题样式
.pie_container .pie_in .pie_wrapper .pie_item .pie_header_col {
	position: relative;
	text-align: center;
	color: #000;
	z-index: 2;
	padding: 6px;
	height: 50px;
	display: flex;
	align-items: center;
	justify-content: center;
}

// 手机竖屏适配：每行最多2个
@media (max-width: 768px) and (orientation: portrait) {
	.pie_container .pie_in .pie_wrapper .pie_item {
		flex: 0 0 50%; // 每个项目占50%宽度，一行显示2个
		max-width: 50%;
	}

	// 偶数个数
	.pie_container .pie_in .pie_wrapper .pie_item:nth-child(even) .pie_header_col::after {
		border: none !important;
	}

	.pie_container .pie_in .pie_wrapper .pie_item:last-child .pie_header_col::after {
		overflow: hidden;
	}
}
// 标题背景效果
.pie_container .pie_in .pie_wrapper .pie_item .pie_header_col::after {
	content: '';
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	background: #ccc;
	z-index: 1;
	transform: scaleX(1) scaleY(1) scaleZ(1) skewX(-30deg);
	transform-origin: 50% 50%;
	box-sizing: border-box;
	border-right: 7px solid #220e75;
	background: linear-gradient(to right, #2974a3, #15cd8f);
}

// 最后一个标题不显示右边框
.pie_container .pie_in .pie_wrapper .pie_item:last-child .pie_header_col::after {
	border: none !important;
}

// 标题文字样式
.pie_container .pie_in .pie_wrapper .pie_item .pie_header_col .pie_header_col_text {
	position: relative;
	color: #fff;
	z-index: 1000;
	background: none;
	font-size: 18px;
}

/* content */
.pie_container .pie_in .pie_wrapper .pie_item .pie_content_col {
	overflow: hidden;
	z-index: 100000;
}
.pie_container .pie_in .pie_wrapper .pie_item .pie_content_col .pie_content_col_chart {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	margin: 30px 0 10px;
	
	color: #23ffc3;
	text-shadow: 0px 2px 0px #16344c;
	/* background-clip: text;
    -webkit_text-fill_color: transparent; */
	:global {
		.css-1yw12s7.ant-progress.ant-progress-circle .ant-progress-text {
			top: 50% !important;
			left: 0 !important;
		}
	}

	.defaultCover {
		user-select: none;
	}
	.defaultCover:active {
		opacity: 0.6;
	}
}
.pie_container
	.pie_in
	.pie_wrapper
	.pie_item
	.pie_content_col
	.pie_content_col_chart
	.pie_content_col_chart_c {
	font-size: 26px;
	color: #23ffc3;
	padding-bottom: 6px;
}
.pie_container
	.pie_in
	.pie_wrapper
	.pie_item
	.pie_content_col
	.pie_content_col_chart
	.pie_content_col_chart_c
	> span:last-child {
	font-size: 16px;
	padding: 0 2px;
}
.pie_container
	.pie_in
	.pie_wrapper
	.pie_item
	.pie_content_col
	.pie_content_col_chart
	.pie_content_col_chart_t {
	font-size: 13px;
	color: #23ffc3;
}
.pie_container .pie_in .pie_wrapper .pie_item .pie_content_col .pie_content_col_time {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-weight: normal;
	font-size: 13px;
	padding: 10px;
	text-align: center;
	box-sizing: border-box;
	background: radial-gradient(
		circle at center,
		RGBA(22, 206, 144, 0.3) 0%,
		rgba(0, 0, 0, 0) 60%,
		rgba(0, 0, 0, 0) 100%
	);
	margin-bottom: 10px;
}

.pie_proxy {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	width: 112px;
	height: 112px;
	border-width: 2px;
	border-style: solid;
	border-color: #ff8b4b;
	/* border-image: linear-gradient(to bottom, #f00, #0f0, #00f) 1; */
	border-radius: 50%;
	box-sizing: border-box;
	background: none;
	padding: 3px;
}
.pie_proxy .pie_proxy_in {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	width: 100%;
	height: 100%;
	border-radius: 50%;
	border: 1px solid #60e7a1;
	box-sizing: border-box;
	box-shadow: inset 0px 0px 14px #60e7a1;
	overflow: hidden;
}
.pie_proxy .pie_proxy_in .triangle {
	position: absolute;
	width: 10px;
	height: 10px;
	background: #60e7a1;
}
.pie_proxy .pie_proxy_in .triangle_t {
	top: -5px;
	left: 50%;
	transform: rotate(45deg) translateX(-50%);
}
.pie_proxy .pie_proxy_in .triangle_b {
	bottom: -12px;
	left: 50%;
	transform: rotate(45deg) translateX(-50%);
}
.pie_proxy .pie_proxy_in .triangle_l {
	top: 50%;
	left: -12px;
	transform: rotate(45deg) translateY(-50%);
}
.pie_proxy .pie_proxy_in .triangle_r {
	top: 50%;
	right: -5px;
	transform: rotate(45deg) translateY(-50%);
}