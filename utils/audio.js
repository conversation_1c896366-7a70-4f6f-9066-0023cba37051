// 导入上传音频错误的函数
import { appOptionsWrapper } from '.';
import { uploadAudioError } from '../services/schedule';

// 定义音频错误类型枚举
export const errorEnum = {
    1: "获取过程被用户中止",
    2: "下载时出错",
    3: "解码时发生错误",
    4: "不支持音频，请重试",
}

// CDN 列表，用于音频加载失败时的切换
const cdnList = [
    "https://gkword.qiaoxuesi.com",
    "https://gkword-v2.qiaoxuesi.com",
    "https://gkword-v3.qiaoxuesi.com",
    "https://gkword-v4.qiaoxuesi.com",
    "https://gkword-v5.qiaoxuesi.com",
    "https://gkword-v6.qiaoxuesi.com",
    "https://gkword-v7.qiaoxuesi.com",
    "https://gkword-v8.qiaoxuesi.com",
    "https://gkword-v9.qiaoxuesi.com",
]

// 检查是否在浏览器环境中
const isBrowser = typeof window !== 'undefined';
let canPlayAble = true

// 创建或获取全局唯一的 audio 对象
if (isBrowser) {
    if (!window.globalAudioElement) {
        window.globalAudioElement = new Audio();
    }
}

// 用于跟踪最后一次 playAudio 调用的 Promise
let lastPlayAudioPromise = null;

// 防抖时间间隔（毫秒）
const DEBOUNCE_INTERVAL = 300;

/**
 * 加载音频
 * @param {string} url - 音频 URL
 * @returns {Promise<void>}
 */
const loadAudio = (url) => new Promise((resolve, reject) => {
    if (!isBrowser) {
        resolve(); // 在服务器端，直接解析 Promise
        return;
    }

    const fullUrl = `${url}${url.includes("?") ? "&" : "?"}t=${Date.now()}`;
    window.globalAudioElement.pause();
    window.globalAudioElement.src = "";
    window.globalAudioElement.src = fullUrl;
    window.globalAudioElement.load();

    const onCanPlay = () => {
        cleanup();
        resolve();
    };

    const onError = (e) => {
        cleanup();
        reject(new Error(`音频加载失败: ${errorEnum[e?.target?.error?.code]}`));
    };

    const cleanup = () => {
        window.globalAudioElement.removeEventListener('canplaythrough', onCanPlay);
        window.globalAudioElement.removeEventListener('error', onError);
    };

    window.globalAudioElement.addEventListener('canplaythrough', onCanPlay);
    window.globalAudioElement.addEventListener('error', onError);
});

/**
 * 尝试使用不同的 CDN 加载音频
 * @param {string} url - 原始音频 URL
 * @returns {Promise<boolean>} - 是否成功加载
 */
const tryWithCDN = async (url) => {
    const usedCDNs = new Set();
    for (let i = 0; i < 3; i++) {
        const availableCDNs = cdnList.filter(cdn => !usedCDNs.has(cdn));
        if (availableCDNs.length === 0) break;

        const randomCDN = availableCDNs[Math.floor(Math.random() * availableCDNs.length)];
        usedCDNs.add(randomCDN);
        const currentCDN = cdnList.find(cdn => url.includes(cdn));
        const newUrl = url.replace(currentCDN, randomCDN);

        try {
            await loadAudio(newUrl);
            return true;
        } catch (e) {
            console.error(`CDN 加载失败: ${newUrl}`, e);
        }
    }
    return false;
};

const doPlay = async (repeatTime = 1, proLite) => {
    if (!canPlayAble) return
    try {
        for (let i = 0; i < repeatTime; i++) {
            if (proLite) {
                await proLite()
            } else {
                window.globalAudioElement.currentTime = 0;
                await window.globalAudioElement.play();
                await new Promise(resolve => window.globalAudioElement.addEventListener('ended', resolve, { once: true }));
            }
        }
    }
    catch (err) {
        throw err
    }
}

/**
 * 尝试使用JsBridgeApp.playAudioProLite播放音频
 * @param {string} url - 原始音频 URL
 */
const tryWithPlayAudioProLite = async (url, onStart, onEnd) => {
    let parent = window.parent || window;
    if (parent.JsBridgeApp && parent.JsBridgeApp.playAudioProLite) {
        return new Promise((resolvePlay, rejectPlay) => {
            try{
                parent.JsBridgeApp.playAudioProLite(appOptionsWrapper({
                    url,
                    cacheFirst: 1,
                    cacheDir:"aiWord",//缓存到哪个文件夹，默认gk-caches
                    onStart: (duration, isCache) => {
                        console.log('尝试优先使用原生缓存播放-start', url);
                        if (isCache) {
                            console.log('资源已提前缓存', url)
                        }
                        onStart?.()
                    },
                    onDownload: (time, process) => {
                        console.log('原生缓存-download', time, process);
                    },
                    onStop: () => {
                        console.log('尝试优先使用原生缓存播放-stop', url);
                        onEnd?.()
                        resolvePlay();
                    },
                    onError: (error) => {
                        console.error(`playAudioProLite 尝试失败：${url}`, error);
                        rejectPlay(error)
                        uploadAudioError(`playAudioProLite 尝试失败：${url}。${error}`);
                    }
                }));
            } catch (e) {
                console.error(`playAudioProLite 捕获失败：${url}`, e);
            }
        });
    } else {
        throw "JsBridgeApp.playAudioProLite 不存在"
    }
};

let playOrder = 0;

/**
 * 播放音频
 * @param {string} url - 音频 URL
 * @param {number} repeatTime - 重复播放次数
 * @param {Function | undefined} changeLoading - 更新加载状态的回调
 * @param {Function | undefined} changePlaying - 更新播放状态的回调
 * @returns {Promise<string | undefined>} - 成功时返回 undefined，失败时返回错误信息字符串
 */
export const playAudio = async (url, repeatTime = 1, changeLoading, changePlaying, secondUrl = null) => {
    if (!isBrowser) {
        return; // 在服务器端，直接返回
    }

    if (lastPlayAudioPromise) {
        lastPlayAudioPromise.cancel();
    }

    canPlayAble = true
    let isCancelled = false;
    const newPromise = new Promise(async (resolve, reject) => {
        window.globalAudioElement.pause();
        await new Promise(r => setTimeout(r, DEBOUNCE_INTERVAL));

        if (isCancelled) return resolve("操作已取消");

        changeLoading?.(true);

        if (window?.JsBridgeApp && window.JsBridgeApp.playAudioProLite) {
            try {
                console.log('尝试优先使用原生缓存播放', url);
                await doPlay(repeatTime, () => tryWithPlayAudioProLite(url, () => {
                    changeLoading?.(false);
                    changePlaying?.(true);
                }, () => {
                    changeLoading?.(false);
                    changePlaying?.(false);
                }))
                resolve(undefined);
            } catch (playAudioProLiteError) {
                console.error(playAudioProLiteError, '，playAudioProLite 尝试失败，尝试使用老方法');
                try {
                    await loadAudio(url);
        
                    changeLoading?.(false);
                    changePlaying?.(true);
        
                    await doPlay(repeatTime)
        
                    resolve(undefined);
                } catch (err) {
                    console.error(err, '，尝试使用 CDN');
        
                    try {
                        const success = await tryWithCDN(url);
        
                        changeLoading?.(false);
                        changePlaying?.(true);
        
                        if (success) {
                            await doPlay(repeatTime)
                            resolve(undefined);
                        } else {
                            throw new Error('所有 CDN 尝试均失败');
                        }
                    } catch (cdnError) {
                        console.error(cdnError, '，旧方法也失败了');

                        // 如果有 secondUrl，尝试使用 secondUrl 作为兜底
                        if (secondUrl && secondUrl !== url) {
                            console.log('尝试使用 secondUrl 作为兜底:', secondUrl);
                            try {
                                const result = await playAudio(secondUrl, repeatTime, changeLoading, changePlaying, null);
                                resolve(result);
                                return;
                            } catch (secondUrlError) {
                                console.error('secondUrl 也播放失败:', secondUrlError);
                            }
                        }

                        reject(`音频播放失败:${url}`);
                    }
                }
            } finally {
                changeLoading?.(false);
                changePlaying?.(false);
            }
        }else{
            try {
                await loadAudio(url);
    
                changeLoading?.(false);
                changePlaying?.(true);
    
                await doPlay(repeatTime)
    
                resolve(undefined);
            } catch (err) {
                console.error(err, '，尝试使用 CDN');
    
                try {
                    const success = await tryWithCDN(url);
    
                    changeLoading?.(false);
                    changePlaying?.(true);
    
                    if (success) {
                        await doPlay(repeatTime)
                        resolve(undefined);
                    } else {
                        throw new Error('所有 CDN 尝试均失败');
                    }
                } catch (cdnError) {
                    console.error(cdnError, '，CDN尝试失败');

                    // 如果有 secondUrl，尝试使用 secondUrl 作为兜底
                    if (secondUrl && secondUrl !== url) {
                        console.log('尝试使用 secondUrl 作为兜底:', secondUrl);
                        try {
                            const result = await playAudio(secondUrl, repeatTime, changeLoading, changePlaying, null);
                            resolve(result);
                            return;
                        } catch (secondUrlError) {
                            console.error('secondUrl 也播放失败:', secondUrlError);
                        }
                    }

                    reject(`音频播放失败:${url}`);
                }
            } finally {
                changeLoading?.(false);
                changePlaying?.(false);
            }
        }
    });

    newPromise.cancel = () => {
        isCancelled = true;
        window.globalAudioElement.pause();
    };

    lastPlayAudioPromise = newPromise;

    try {
        return await newPromise;
    } catch (error) {
        throw error;
    }
};

/**
 * 停止当前正在播放的音频
 */
export const stopAllSound = () => {
    if (isBrowser && window.globalAudioElement) {
        canPlayAble = false
        window.globalAudioElement.pause();
        window.globalAudioElement.currentTime = 0;
    }
};
