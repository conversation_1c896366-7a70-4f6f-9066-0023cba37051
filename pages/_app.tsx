import App from 'next/app';
import Head from 'next/head';
import { clientConfig, GKAppProvider } from '@gankao/front';
import { ConfigProvider, message } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import moment from 'moment';
import 'moment/locale/zh-cn';
// css(所有的全局css必须从这个文件下导入)
import '../styles/styles.scss';
import { createCache, extractStyle, StyleProvider, legacyLogicalPropertiesTransformer } from '@ant-design/cssinjs';
// 配置moment中文模式
moment.locale('zh-cn');
import dayjs from 'dayjs';
import DayjsDuration from 'dayjs/plugin/duration';
dayjs.extend(DayjsDuration);
import 'core-js/stable';
import 'regenerator-runtime/runtime';
import '@babel/polyfill';
import 'relative-indexing-method-polyfill';
import { appOptionsWrapper, LoadScript, runTimeEnv } from '../utils';
import Router from 'next/router';
import { checkModuleAccessForPartner } from '../services/schedule';
import React, { PropsWithChildren, createContext, useState, useEffect } from 'react';
// 导入修改后的NetworkStatusMonitor组件
import NetworkStatusMonitor, { NetworkMonitorAPI } from '../components/NetworkStatusMonitor';

// 创建全局 context
export const GlobalContext = createContext<{
  version: string;
  setVersion: (v: number) => void;
}>({
  version: '',
  setVersion: (v: number) => {},
});

function __runTime_new() {
  // var UA = window.navigator.userAgent;
  // var ret: any = {};
  // ret.inBaoDanShell = UA.indexOf('gankao') !== -1 && UA.indexOf('baodan') !== -1;
  // ret.isGankaoSDKModule = UA.indexOf('gankaoSDKModule') !== -1;
  // ret.inGankaoPad = UA.indexOf('gankaoHD') !== -1 || UA.indexOf('gankaoPAD') !== -1;
  // ret.inAppSDK = UA.indexOf('gankao') !== -1 && UA.indexOf('SDK') === -1;
  // ret.inSDK = UA.indexOf('gankao') !== -1 && UA.indexOf('SDK') !== -1;
  // ret.inxiaozhuliapp = UA.indexOf('gkagent') !== -1;
  // ret.inWeixin = UA.indexOf('MicroMessenger') !== -1;
  // ret.gankaoTV = UA.indexOf('gankaotv') !== -1;
  // ret.isQQ = UA.indexOf('QQ') !== -1;
  // ret.isGankaoAiPad = UA.indexOf('gankaoAiPad') !== -1;
  // ret.isAndroid = UA.toLowerCase().indexOf('android') !== -1;
  // ret.iOS = (UA.indexOf('iPad') !== -1 || UA.indexOf('iPhone') !== -1) && UA.indexOf('gankaoAiPad') === -1;
  // ret.isOnPc = !/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent);
  // ret.isSJSH = UA.toLowerCase().indexOf('sjsh') !== -1;
  // ret.isSmartclass = UA.toLowerCase().indexOf('smartclass') !== -1;
  // ret.inPartnerApp = UA.toLowerCase().indexOf('gankaopartner_') !== -1;
  // // 用于跳转新版首页
  // ret.isGankaoAiPadPhoneExpress = UA.indexOf('gankaoAiPadPhoneExpress') !== -1;
  return runTimeEnv();
}

//@ts-ignore
Router.onRouteChangeComplete = () => {
  //@ts-ignore
  //触发微信jsSDK的重新注册
  if (window.__reconfigWxShareSDK) window.__reconfigWxShareSDK();
};

// Stagewise工具栏配置
const initStagewiseToolbar = () => {
  if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
    import('@stagewise/toolbar').then(({ initToolbar }) => {
      const stagewiseConfig = {
        plugins: []
      };
      initToolbar(stagewiseConfig);
    }).catch(err => {
      console.warn('Failed to initialize Stagewise toolbar:', err);
    });
  }
};

// 定义ErrorBoundary的状态接口
interface ErrorBoundaryState {
  hasError: boolean;
  errorMsg: string;
  componentStack: string;
  statusCode: number | null;
  route: string;
  uid: string | null;
  deviceInfo: {
    userAgent?: string;
    platform?: string;
    browserName?: string;
    browserVersion?: string;
  };
}

class ErrorBoundary extends React.Component<PropsWithChildren, ErrorBoundaryState> {
  constructor(props: any) {
    super(props);
    this.state = {
      hasError: false,
      errorMsg: '',
      componentStack: '',
      statusCode: null,
      route: '',
      uid: null,
      deviceInfo: {},
    };
  }

  // 获取设备信息的辅助方法
  getDeviceInfo() {
    if (typeof window === 'undefined') return {};
    
    const userAgent = window.navigator.userAgent;
    let browserName = '未知';
    let browserVersion = '未知';
    let platform = '其他';
    
    try {
      // 简单的浏览器检测
      if (/chrome/i.test(userAgent)) {
        browserName = 'Chrome';
      } else if (/firefox/i.test(userAgent)) {
        browserName = 'Firefox';
      } else if (/safari/i.test(userAgent) && !/chrome/i.test(userAgent)) {
        browserName = 'Safari';
      } else if (/msie|trident/i.test(userAgent)) {
        browserName = 'IE';
      } else if (/edge/i.test(userAgent)) {
        browserName = 'Edge';
      }
      
      // 尝试提取版本号
      const versionMatch = userAgent.match(/(?:chrome|firefox|safari|msie|rv|edge)[\/: ](\d+(\.\d+)?)/i);
      browserVersion = versionMatch ? versionMatch[1] : '未知';
      
      // 操作系统检测
      if (/ipad/i.test(userAgent)) {
        if (/gankao/i.test(userAgent)) platform = 'iPad App';
        else platform = 'iPad';
      } else if (/iphone/i.test(userAgent)) {
        if (/gankao/i.test(userAgent)) platform = 'iPhone App';
        else platform = 'iPhone';
      } else if (/android/i.test(userAgent)) {
        if (/gankao/i.test(userAgent)) platform = 'Android App';
        else platform = 'Android';
      } else if (/mac os/i.test(userAgent)) {
        platform = 'Mac OS';
      } else if (/windows/i.test(userAgent)) {
        platform = 'Windows';
      } else if (/linux/i.test(userAgent)) {
        platform = 'Linux';
      }
    } catch (e) {
      console.error('获取设备信息失败', e);
    }
    
    return {
      userAgent,
      browserName,
      browserVersion,
      platform
    };
  }

  static getDerivedStateFromError(error: any) {
    // 更新状态允许渲染错误界面
    let route = '';
    let uid = null;
    let deviceInfo = {};
    
    // 尝试获取当前路由
    if (typeof window !== 'undefined') {
      route = window.location.pathname + window.location.search;
      
      // 获取设备信息
      const userAgent = window.navigator.userAgent;
      deviceInfo = {
        userAgent
      };
      
      // 尝试从多个位置获取用户信息
      try {
        // 1. 从localStorage获取
        const userInfo = localStorage.getItem('userInfo');
        if (userInfo) {
          const parsedUserInfo = JSON.parse(userInfo);
          uid = parsedUserInfo.uid || parsedUserInfo.gankaoUID || parsedUserInfo.userID || null;
        }
        
        // 2. 从window._pkGankaoUID获取（由PK服务设置）
        if (!uid && (window as any)._pkGankaoUID) {
          uid = (window as any)._pkGankaoUID;
        }
        
        // 3. 从window.__userInfo获取
        if (!uid && (window as any).__userInfo) {
          const userInfo = (window as any).__userInfo;
          uid = userInfo.gankaoUID || userInfo.uid || userInfo.userID || userInfo.userId || null;
        }
        
        // 4. 从window.__gankaoUID获取
        if (!uid && (window as any).__gankaoUID) {
          uid = (window as any).__gankaoUID;
        }
        
        // 5. 从sessionStorage获取
        if (!uid) {
          const userInfo = sessionStorage.getItem('userInfo');
          if (userInfo) {
            try {
              const parsedUserInfo = JSON.parse(userInfo);
              uid = parsedUserInfo.uid || parsedUserInfo.gankaoUID || parsedUserInfo.userID || null;
            } catch (e) {
              console.error('解析sessionStorage userInfo失败', e);
            }
          }
        }
        
        // 6. 从localStorage的其他可能位置获取
        if (!uid) {
          const gankaoPageSession = localStorage.getItem('gankaoPageSession_userInfo');
          if (gankaoPageSession) {
            try {
              const parsedSession = JSON.parse(gankaoPageSession);
              uid = parsedSession.uid || parsedSession.gankaoUID || parsedSession.userID || null;
            } catch (e) {
              // 可能不是JSON格式
              console.error('解析gankaoPageSession_userInfo失败', e);
            }
          }
        }
      } catch (e) {
        console.error('获取用户信息失败', e);
      }
    }
    
    return {
      hasError: true,
      statusCode: error.statusCode || 500,
      errorMsg: error.message || '',
      componentStack: '',
      route,
      uid,
      deviceInfo,
    };
  }

  componentDidCatch(error: any, errorInfo: any) {
    console.error('ErrorBoundary caught an error', error?.message, errorInfo);

    // 获取当前路由和用户UID
    let route = '';
    let uid = null;
    
    if (typeof window !== 'undefined') {
      route = window.location.pathname + window.location.search;
      
      // 尝试从多个位置获取用户信息
      try {
        // 1. 从localStorage获取
        const userInfo = localStorage.getItem('userInfo');
        if (userInfo) {
          const parsedUserInfo = JSON.parse(userInfo);
          uid = parsedUserInfo.uid || parsedUserInfo.gankaoUID || parsedUserInfo.userID || null;
        }
        
        // 2. 从window._pkGankaoUID获取（由PK服务设置）
        if (!uid && (window as any)._pkGankaoUID) {
          uid = (window as any)._pkGankaoUID;
        }
        
        // 3. 从window.__userInfo获取
        if (!uid && (window as any).__userInfo) {
          const userInfo = (window as any).__userInfo;
          uid = userInfo.gankaoUID || userInfo.uid || userInfo.userID || userInfo.userId || null;
        }
        
        // 4. 从window.__gankaoUID获取
        if (!uid && (window as any).__gankaoUID) {
          uid = (window as any).__gankaoUID;
        }
        
        // 5. 从sessionStorage获取
        if (!uid) {
          const userInfo = sessionStorage.getItem('userInfo');
          if (userInfo) {
            try {
              const parsedUserInfo = JSON.parse(userInfo);
              uid = parsedUserInfo.uid || parsedUserInfo.gankaoUID || parsedUserInfo.userID || null;
            } catch (e) {
              console.error('解析sessionStorage userInfo失败', e);
            }
          }
        }
        
        // 6. 从localStorage的其他可能位置获取
        if (!uid) {
          const gankaoPageSession = localStorage.getItem('gankaoPageSession_userInfo');
          if (gankaoPageSession) {
            try {
              const parsedSession = JSON.parse(gankaoPageSession);
              uid = parsedSession.uid || parsedSession.gankaoUID || parsedSession.userID || null;
            } catch (e) {
              // 可能不是JSON格式
              console.error('解析gankaoPageSession_userInfo失败', e);
            }
          }
        }
      } catch (e) {
        console.error('获取用户信息失败', e);
      }
    }

    // 记录错误信息
    this.setState({
      errorMsg: error?.message,
      componentStack: errorInfo?.componentStack,
      route,
      uid,
      deviceInfo: this.getDeviceInfo(),
    });

    // 可以在这里添加错误上报逻辑
    if (typeof window !== 'undefined') {
      // 发送错误到监控系统
      // 例如: sendErrorToMonitoring(error, errorInfo);

      // 记录到控制台，方便开发调试
      console.group('React Error Boundary');
      console.error('Error:', error);
      console.error('Component Stack:', errorInfo?.componentStack);
      console.error('Route:', route);
      console.error('UID:', uid);
      console.error('Device Info:', this.getDeviceInfo());
      console.groupEnd();
    }
  }

  render() {
    if (this.state.hasError) {
      // 使用自定义错误UI
      return (
        <div
          style={{
            backgroundColor: '#1890ff',
            color: 'white',
            minHeight: '100vh',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            fontFamily:
              '-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif',
            padding: '20px',
            textAlign: 'center',
          }}
        >
          <div
            style={{
              maxWidth: '600px',
              padding: '40px',
              borderRadius: '8px',
              backgroundColor: 'rgba(0, 60, 136, 0.5)',
              boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)',
            }}
          >
            <h1
              style={{
                fontSize: '32px',
                marginBottom: '20px',
                fontWeight: '500',
              }}
            >
              应用程序错误
            </h1>
            <p
              style={{
                fontSize: '18px',
                lineHeight: '1.6',
                marginBottom: '30px',
              }}
            >
              抱歉，应用程序遇到了意外错误。
            </p>
            {/* 始终显示错误信息，不再根据环境判断 */}
            <div
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.2)',
                padding: '15px',
                borderRadius: '4px',
                marginBottom: '20px',
                textAlign: 'left',
                overflow: 'auto',
                maxHeight: '300px',
              }}
            >
              {this.state.errorMsg && (
                <div style={{ margin: '5px 0' }}>
                  <span style={{ fontWeight: 'bold' }}>错误信息: </span>
                  <span style={{ wordBreak: 'break-word' }}>{this.state.errorMsg}</span>
                </div>
              )}
              {this.state.route && (
                <div style={{ margin: '5px 0' }}>
                  <span style={{ fontWeight: 'bold' }}>路由信息: </span>
                  <span style={{ wordBreak: 'break-word' }}>{this.state.route}</span>
                </div>
              )}
              {this.state.uid && (
                <div style={{ margin: '5px 0' }}>
                  <span style={{ fontWeight: 'bold' }}>用户UID: </span>
                  <span style={{ wordBreak: 'break-word' }}>{this.state.uid}</span>
                </div>
              )}
              {/* 移除设备信息显示，只保留用户UID */}
              {/* 只在开发环境显示组件堆栈信息 */}
              {this.state.componentStack && (
                <div style={{ margin: '5px 0' }}>
                  <p style={{ margin: '0 0 0 0', fontWeight: 'bold' }}>组件堆栈:</p>
                  <pre style={{ margin: 0, whiteSpace: 'pre-wrap', wordBreak: 'break-word', fontSize: '12px' }}>{this.state.componentStack}</pre>
                </div>
              )}
            </div>
            <div>
              <button
                onClick={() => window.location.reload()}
                style={{
                  backgroundColor: 'white',
                  color: '#1890ff',
                  border: 'none',
                  padding: '10px 20px',
                  borderRadius: '4px',
                  fontSize: '16px',
                  cursor: 'pointer',
                  fontWeight: '500',
                  transition: 'all 0.3s ease',
                  marginRight: '10px',
                }}
                onMouseOver={e => (e.currentTarget.style.backgroundColor = '#f0f0f0')}
                onMouseOut={e => (e.currentTarget.style.backgroundColor = 'white')}
              >
                刷新页面
              </button>
              <button
                onClick={() => (window.location.href = '/p-aiword/landing')}
                style={{
                  backgroundColor: 'transparent',
                  color: 'white',
                  border: '1px solid white',
                  padding: '10px 20px',
                  borderRadius: '4px',
                  fontSize: '16px',
                  cursor: 'pointer',
                  fontWeight: '500',
                  transition: 'all 0.3s ease',
                }}
                onMouseOver={e => {
                  e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                }}
                onMouseOut={e => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                返回首页
              </button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * 封装主体页面结构的组件，作为整个App的根
 */
interface MyAppState {
  version: string;
}

class MyApp extends App<MyAppState> {
  state: MyAppState = {
    version: '',
  };

  static getInitialProps = async (appContext: any) => {
    const appProps = await App.getInitialProps(appContext);
    const { ctx } = appContext;
    const { req } = ctx || {};
    // 部分内容由中间件携带
    const { __partner, __partner_error, __userInfo, __gankaoUID } = req || {};

    return { ...appProps };
  };

  setVersion: (v: number) => void;
  
  // 引用网络监控API
  private networkMonitorRef = React.createRef<NetworkMonitorAPI>();

  constructor(props: any) {
    super(props);
    this.state = {
      version: '',
    };
    this.setVersion = (v: number) => {
      this.setState({ version: v });
    };
  }

  componentDidMount() {
    console.log(`项目启动完成`);
    // @ts-ignore
    window.__runTime_new = __runTime_new;
    // @ts-ignore
    window.__runTime = __runTime_new;
    
    // @ts-ignore
    const { inWeixin, isAndroid, isGankaoAiPadPhoneExpress, isVIP, isGankaoAiPad } =
      // @ts-ignore
      window?.__runTime_new() || {};

    if (!isGankaoAiPad) {
      console.log('>>>>app entry加载AI单词斩');
      setTimeout(() => {
        checkModuleAccessForPartner((result: any, error: any) => {
          if (error) {
            console.error('访问检查失败', error);
            return;
          }
          console.log('模块访问状态:', result);
          // if (result.result.access === false) {
          // } else {
          // }
        });
      }, 500);
    }
    if (isVIP) {
      this.setVersion(1);
    } else {
      this.setVersion(0);
    }
    // @ts-ignore
    if (inWeixin && !window.wx && !isAndroid) {
      LoadScript('https://res2.wx.qq.com/open/js/jweixin-1.6.0.js', () => {
        console.log('>>>>app entry加载微信jssdk成功');
        let defaultAppId = 'wx80be73fe9a5e0537';
        let host = location.host;
        if (host && host.indexOf('sshdschool273') >= 0) {
          defaultAppId = 'wx658954d5f120c3a7';
        }
        LoadScript(`https://${host}/p-wx/wx_config/${defaultAppId}`, () => {
          console.log('>>>>app entry加载微信config成功');
        });
      });
    }

    window.JsBridgeApp?.checkCurrentAppPermission &&
      window.JsBridgeApp?.checkCurrentAppPermission(
        appOptionsWrapper({
          //录音权限：android.permission.RECORD_AUDIO
          //拍照权限：android.permission.CAMERA
          mediaType: 'android.permission.RECORD_AUDIO', //camera=相机、microphone=麦克风
          requestUserAgreeWhenNoPermisson: false, //当检测无权限时，是否原生直接向用户申请授权操作
          onCheckResult: (result: { err: any; hasPermisson: any }) => {
            const { err, hasPermisson } = result;
            if (err) {
              // message.error(err?.message || err);
              console.log(err?.message || err);
            }
            if (!~~hasPermisson) {
              // message.warning("请先授予麦克风权限");
              localStorage.setItem('mic_forbidden', '1');
            } else {
              localStorage.removeItem('mic_forbidden');
            }
            //err：错误信息
            //hasPermisson：App当前是否已获得权限 0未获得 1已获得
          },
          onRequestUserResult: (result: { err: any; isAgreed: any }) => {
            const { err, isAgreed } = result;
            //err：错误信息，当isAgreed=false时有参考
            //isAgreed：用户是否同意了授权 0拒绝 1同意
            if (err) {
              // message.error(err?.message || err);
              console.log(err?.message || err);
            }
            if (!~~isAgreed) {
              // message.warning("请先授予麦克风权限~~,否则无法使用读单词功能");
              localStorage.setItem('mic_forbidden', '1');
            } else {
              localStorage.removeItem('mic_forbidden');
            }
          },
        }),
      );

    // if (!isGankaoAiPadPhoneExpress) {
    //     console.log('window.location.pathname',window.location.pathname)
    //     if (window.location.pathname === "/p-aiword/landing") {
    //         // 跳转判断
    //         Router.replace({
    //             pathname: "/landing",
    //             query: {
    //                 formHomeV2: 1,
    //             },
    //         });
    //     }
    // }

    // 初始化 version
    // if (typeof window !== 'undefined') {
    //   const { gankaoxueban } = runTimeEnv();
    //   this.setVersion(gankaoxueban ? 0 : 1);
    // }

    // 初始化Stagewise工具栏（仅在开发环境）
    initStagewiseToolbar();
  }

  render() {
    const { Component, pageProps } = this.props;
    return (
      <GlobalContext.Provider value={{ version: this.state.version, setVersion: this.setVersion }}>
        <GKAppProvider mainSite={clientConfig().gankao_main_site}>
          <ConfigProvider
            locale={zhCN}
            theme={{
              token: {
                colorPrimary: '#8070F5',
              },
            }}
          >
            <Head>
              <meta charSet='UTF-8' />
              <meta name='viewport' content='width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no' />
              <meta content='telephone=no' name='format-detection' />
              <meta content='yes' name='apple-mobile-web-app-capable' />
              <meta httpEquiv='Expires' content='0' />
              <meta httpEquiv='Pragma' content='no-cache' />
              <meta httpEquiv='Cache-control' content='no-cache' />
              <meta httpEquiv='Cache' content='no-cache' />
              <meta name='gankao_noTitleBar' content='1' /> ,
              <meta name='gankao_hideTitleBar_showLeftNav' content='1' />
              <meta name='gankao_andoird_bottom_nav_home_disbaled' content='1' />
              <title>AI单词斩</title>
            </Head>
            <StyleProvider hashPriority='high' transformers={[legacyLogicalPropertiesTransformer]}>
              <ErrorBoundary>
                {/* 添加NetworkStatusMonitor组件，不再使用NetworkProvider */}
                <NetworkStatusMonitor 
                  disableOfflineAlert={false}
                  disablePoorConnectionAlert={true}
                  alertClosable={true}
                  showFloatingIndicator={true}
                  showSignalIcon={true}
                />
                <Component {...pageProps} />
              </ErrorBoundary>
            </StyleProvider>
          </ConfigProvider>
        </GKAppProvider>
      </GlobalContext.Provider>
    );
  }
}

export default MyApp;
