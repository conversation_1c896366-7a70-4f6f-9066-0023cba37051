@import '@/styles/word-pk.scss';

@function relativeSize($size) {
  @return calc(100vw * $size / 1920);
}

.check {
  > :not(:first-child) {
      margin-left: 4px;
  }
}

.page {
  // background-image: url(https://img.qiaoxuesi.com/files/698_SketchPnge43969ab97b2b552eb48eb9dcaddde98e574c739b43063602e41a76190d9e951.png) !important;
  background-size: cover;
  background-image: linear-gradient(0deg, #6f53e3, #6f53e3);
  overflow: auto;
  position: relative;

  .headerCenter {
    @include mobile {
      max-width: 60vw;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }

  :global {
    .app-page-header {
      padding: 0 2.08vw;
      height: 60px;
    }

    .ant-table-cell {
      background: #5338c4 !important;
    }

    .app-page-body {
      height: max-content;
      padding: 0 2%;

      @include mobile {
        margin-top: 0 !important;
      }
    }
  }
  .header_left {
    > .exit_btn {
      height: 3.65vw;
      object-fit: contain;
    }
  }
  .header_center {
    white-space: nowrap;
    height: 50px;
    font-size: 21px;
    font-family: zihun144hao-langyuanti, zihun144hao;
    font-weight: normal;
    color: #ffffff;
    line-height: 50px;
    letter-spacing: 1px;
    text-shadow: 0px 2px 1px #4e34be;
    -webkit-text-stroke: 0px #4225bd;

    .user_name {
      display: inline;
      max-width: 140px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 5px;
    }
  }
  .header_right {

    @include mobile {
      margin-top: 10px !important;
      right: 0 !important;
    }

    .page_extra_btn {
      background: rgba(255, 255, 255, 0.18);
      border-radius: 500px;
      border: 1px solid rgba(255, 255, 255, 0);
      padding: 5px 20px;
      font-size: 14px;
      color: #ffffff;
      line-height: 1.5;
    }
  }

  .secondary_title {
    font-size: 11px;
    font-family: zihun144hao-langyuanti, zihun144hao;
    font-weight: normal;
    color: #ffffff;
    text-shadow: 0px 2px 1px #4e34be;
    -webkit-text-stroke: 0px #4225bd;
    text-stroke: 0px #4225bd;

    @include ipadYST {
      font-size: 1rem;
    }
  }

  .page_content {
    height: 100%;
    border-radius: 10px;
    padding: 0px 20px 10px 20px;
    overflow-y: auto;

    .grade {
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 43.8vh;
      position: relative;

      @include mobile {
        height: 30vh;
      }

      .rewardBadge {
        height: 30vh;

        @include mobile {
          height: unset;
        }
      }

      .encouragement {
        font-size: 16px;

        color: #ffffff;
        line-height: 20px;

        @include ipadYST {
          font-size: 1.2rem;
        }
      }

      .footer {
        position: absolute;
        bottom: 1px;
        left: 0;
        right: 0;
        margin: auto;

        width: 51.7vw;
        height: 38px;
        background: #371ea7;
        box-shadow: inset 0px 1px 0px 0px rgba(78, 48, 214, 1);
        border-radius: 10px 10px 0px 0px;
        text-align: center;
        padding: 0 17px;
        padding-top: 1px;
        display: flex;
        justify-content: center;

        @include mobile {
          width: 65vw;
        }

        .date {
          display: inline-block;
          // margin-left: 20px;
          font-size: 16px;

          color: rgba(255, 255, 255, 0.6);
          line-height: 38px;
          @include ipadYST {
            font-size: 1.2rem;
          }
        }
        .ellapsed {
          display: inline-block;
          font-size: 16px;

          color: rgba(255, 255, 255, 0.6);
          line-height: 38px;
        }
      }

      .goFlashTest {
        position: absolute;
        top: 30%;
        right: 10px;
        width: 109px;
        height: 67px;
        background-color: rgba(255, 175, 67, 0.31);
        border-radius: 15px;
        border: 1px solid rgba(255, 175, 67, 0.45);
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: center;

        .coin {
          height: 31px;
        }
        .desc {
          font-size: 14px;

          color: #ffffff;
          line-height: 14px;

          > img {
            vertical-align: bottom;
            margin-left: 5px;
          }
        }
      }
    }

    .today_review {
      font-size: 13px;

      color: #ffffff;
      line-height: 20px;
      letter-spacing: 1px;

      @include ipadYST {
        font-size: 1.2rem;
      }
    }
    .statistic_content {
      > :not(:first-child) {
        margin-top: 30px;
      }
      .statistic_content_number {
        display: inline-block;
        font-size: 40px;
        color: #62da73;

        @include mobile {
          font-size: 2rem;
        }
      }
      .day_marker {
        color: #ffffff;
        font-size: 25px;

        @include mobile {
          font-size: 1.5rem;
        }
      }
      .schedule_info {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.6);

        @include ipadYST {
          font-size: 1.2rem;
        }
      }

      .staistic_list {
        color: #ffffff;
        .staistic_item {
          display: inline-block;
          .staistic_item_title {
            font-size: 20px;
            color: #ffffff;
          }
          .staistic_item_desc {
            font-size: 20px;
            .statistic_content_number {
              font-size: 20px;
              margin: 0 3px;
            }
            color: #ffffff;
          }
        }
        > :not(:first-child) {
          margin-left: 2.5vw;

          @include mobile {
            margin: 10px 0 0 0;
          }
        }
      }
    }

    .step_detail {
      @include ipadYST {
        zoom: 1.5;
      }
      .step_detail_wrapper {
        margin-top: 8px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .step_detail_item {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          .progress_cirle {
            width: 21.8vw;
            height: 21.8vw;
          }
          .step_ellasped {
            background-repeat: no-repeat;
            background-size: 100% 100%;
          }
        }
      }
    }

    .ans_content {
      pointer-events: none;
      // overflow-x: auto;
      @include ipadYST {
        zoom: 2;
      }
      :global {
        .ant-table {
          background: transparent !important;
          color: #ffffff;
        }
        .ant-table-placeholder {
          background: transparent !important;
          color: rgba(255, 255, 255, 0.4);
        }
        .ant-table-wrapper {
          * {
            border-color: rgba(255, 255, 255, 0.2) !important;
          }
        }
        .ant-table-cell-row-hover {
          background: #5338c4 !important;
        }
        .ant-table-thead > tr > td,
        th {
          background: #5338c4 !important;
        }
        .css-dev-only-do-not-override-1yw12s7.ant-table-wrapper
          .ant-table-thead
          > tr
          > td:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
          background-color: unset !important;
        }
      }
    }
  }
}

.block_wrapper {
  background: #5338c4;
  border-radius: 10px;
  border: 1px solid #7f69da;
  padding: 20px 10px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  height: 100%;
  position: relative;
  overflow-x: auto;

  .block_title {
    font-size: 18px;

    font-weight: normal;
    color: rgba(255, 255, 255, 0.6);
    line-height: 18px;
    letter-spacing: 1px;
    text-shadow: 0px 2px 1px #4e34be;
    -webkit-text-stroke: 0px #4225bd;
    text-align: center;
  }

  .ai_tips {
    position: absolute;
    bottom: 0;
    right: 1vw;
    color: rgba(255, 255, 255, 0.5);
  }

  .header {
    .refresh_icon {
      cursor: pointer;
      color: rgba(0, 0, 0, 0.65);

      &:hover {
        color: rgba(0, 0, 0, 0.85);
      }

      // 添加旋转动画
      &.rotating {
        animation: rotate360 1s linear;
      }
    }
    .header_title::before {
      width: 3px;
      border-radius: 3px;
      height: 1rem;
      background: #ffffff;
      content: ' ';
      display: inline-flex;
      margin-right: 1rem;
    }
    .header_title {
       display: inline-flex;
      align-items: center;
      font-size: 15px;
      color: #ffffff;
      line-height: 15px;
      .header_second {
        margin-left: -2rem;
        display: inline-flex;
        align-items: center;
        font-size: 15px;
        color: #ffffff;
        line-height: 15px;
      }
      span {
        color: rgba($color: #fff, $alpha: 0.6);
        font-size: 12px;
        @include ipadYST {
          font-size: 1.2rem;
        }
      }

      @include ipadYST {
        font-size: 1.5rem;
        font-weight: bold;
      }
    }
    .header_extra {
      display: inline-flex;
      position: absolute;
      right: 20px;

      font-size: 14px;

      color: rgba(255, 255, 255, 0.6);
      line-height: 14px;
      vertical-align: middle;

      .header_extra_item {
        display: flex;
        align-items: center;
      }
    }
  }

  .statistic_card {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    flex-direction: column;
    height: relativeSize(160);
    background: #593cd0;
    border-radius: relativeSize(20);

    @include mobile {
      zoom: 3;
    }
    .title {
      height: relativeSize(55);
      font-size: relativeSize(55);
      font-family: DFPYuanW7;
      color: #ffffff;
      line-height: relativeSize(55);
      letter-spacing: 1px;
      display: flex;
      align-items: flex-end;

      .unit {
        font-size: relativeSize(28.8);
        line-height: relativeSize(28.8);
        display: flex;
      }
    }
    .desc {
      height: relativeSize(26);
      font-size: relativeSize(26);
      font-family: DFPYuanW7;
      color: rgba(255, 255, 255, 0.6);
      line-height: relativeSize(26);
      margin-top: relativeSize(14);
    }
  }
  .statistic_card:not(:first-of-type) {
    margin-top: relativeSize(30);

    .title {
      font-size: relativeSize(42);
    }
  }
}

.square_badge {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border-radius: 3px;
  margin-right: 0.5rem;
}

.calendarCell {
}

.calendar_wrapper {
  width: 40vw;
  .learning_record {
    .learning_record_item {
      height: 44px;
      background: rgb(228, 228, 228);
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16px;

      color: #ffffff;
      line-height: 16px;
      letter-spacing: 1px;
      border: 1px solid white;
    }
  }
}

@media (orientation: landscape) and (max-height: 475px) {
  .page {
    :global {
      .app-page-body {
        padding: 0 1%;
      }
    }
  }
  .page_content {
    padding: 0 10px !important;
  }
  .grade {
    height: 57.8vh !important;
  }
  .rewardBadge {
    height: 41vh !important;
  }
  .step_detail {
    zoom: 0.65;
  }
}

@keyframes rotate360 {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 分享弹窗样式
.modal {
  :global {
    .ant-modal-content {
      background: transparent;
      box-shadow: none;
    }
  }
}

.codeImg {
  // background: #fff;
  // padding: 24px;
  border-radius: 12px;
  text-align: center;
  // box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

// 单词翻转动画样式
.wordFlipContainer {
  // cursor: pointer;
  pointer-events: all;
  perspective: 1000px;
  height: 100%;
  display: flex;
  align-items: center;
  min-width: 100px;
  max-width: 40vw;
}

.wordFlipCard {
  position: relative;
  width: 100%;
  height: 40px;
  transform-style: preserve-3d;
  transition: transform 0.6s ease-in-out;
  
  &.flipped {
    transform: rotateX(180deg);
  }
}

.wordFront,
.wordBack {
  // position: absolute;
  width: 100%;
  // height: 100%;
  backface-visibility: hidden;
  // display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  padding: 0 8px;
  box-sizing: border-box;
}

.wordFront {
  background: transparent;
  color: #ffffff;
  z-index: 2;
}

@include mobile {
  .statistic_card {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    flex-direction: column;
    height: relativeSize(160);
    background: #593cd0;
    border-radius: relativeSize(20);

    @include mobile {
      zoom: 3;
    }
    .title {
      height: relativeSize(55);
      font-size: relativeSize(55);
      font-family: DFPYuanW7;
      color: #ffffff;
      line-height: relativeSize(55);
      letter-spacing: 1px;
      display: flex;
      align-items: flex-end;

      .unit {
        font-size: relativeSize(28.8);
        line-height: relativeSize(28.8);
        display: flex;
      }
    }
    .desc {
      height: relativeSize(26);
      font-size: relativeSize(26);
      font-family: DFPYuanW7;
      color: rgba(255, 255, 255, 0.6);
      line-height: relativeSize(26);
      margin-top: relativeSize(14);
    }
  }
  .statistic_card:not(:first-of-type) {
    margin-top: relativeSize(30);

    .title {
      font-size: relativeSize(42);
    }
  }
}

.wordBack {
  // background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  // transform: rotateX(180deg);
  padding: 2px 8px;
  font-size: 12px;
  text-align: center;
  // border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
  // line-height: 40px;
  
  // 单行超出省略号
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  
  @include ipadYST {
    font-size: 1rem;
  }
}
